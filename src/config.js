/**
 * 视频速度控制器配置文件
 * 精简版本 - 仅包含核心功能配置
 */

// 固定快捷键配置
export const SHORTCUTS = {
    increase: 'c',           // 加速播放
    decrease: 'x',           // 减速播放
    reset: 'z',             // 重置速度
    fullscreen: 'v',        // 网页全屏
    seekForward: 'ArrowRight',    // 快进
    seekBackward: 'ArrowLeft'     // 快退
};

// 媒体控制配置
export const MEDIA_CONFIG = {
    SPEED_STEP: 0.25,       // 速度调整步长
    MIN_SPEED: 0.25,        // 最小播放速度
    MAX_SPEED: 3.0,         // 最大播放速度
    DEFAULT_SPEED: 1.0,     // 默认播放速度
    SEEK_STEP: 5            // 快进/快退步长（秒）
};

// UI配置
export const UI_CONFIG = {
    INDICATOR_ID: 'video-speed-indicator',
    LIGHTBOX_OVERLAY_ID: 'video-lightbox-overlay',
    INDICATOR_DISPLAY_TIME: 1500,  // 指示器显示时间
    Z_INDEX: 2147483647            // 最高层级
};

export default {
    SHORTCUTS,
    MEDIA_CONFIG,
    UI_CONFIG
};
