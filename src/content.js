/**
 * 视频速度控制器 - 极简版
 * 集成所有核心功能的单文件实现
 */

(function () {
    'use strict';

    // 配置常量
    const SHORTCUTS = {
        increase: 'c',
        decrease: 'x',
        reset: 'z',
        fullscreen: 'v',
        seekForward: 'ArrowRight',
        seekBackward: 'ArrowLeft'
    };

    const MEDIA_CONFIG = {
        SPEED_STEP: 0.25,
        MIN_SPEED: 0.25,
        MAX_SPEED: 3.0,
        DEFAULT_SPEED: 1.0,
        SEEK_STEP: 5
    };

    const UI_CONFIG = {
        INDICATOR_ID: 'video-speed-indicator',
        SPEED_OVERLAY_ID: 'video-speed-overlay',
        LIGHTBOX_OVERLAY_ID: 'video-lightbox-overlay',
        INDICATOR_DISPLAY_TIME: 1500,
        SPEED_DISPLAY_TIME: 2000,
        Z_INDEX: 2147483647
    };

    // 全局变量
    let currentVideo = null;
    let lightboxOverlay = null;
    let speedIndicator = null;
    let speedOverlay = null; // 视频左上角的速度提示
    let isLightboxActive = false;
    let originalVideoState = null; // 保存原始视频状态

    /**
     * 初始化插件
     */
    function init() {
        createIndicator();
        setupKeyboardListeners();
        console.log('视频速度控制器已启动 (极简版)');
    }

    /**
     * 创建状态指示器
     */
    function createIndicator() {
        if (document.getElementById(UI_CONFIG.INDICATOR_ID)) return;

        speedIndicator = document.createElement('div');
        speedIndicator.id = UI_CONFIG.INDICATOR_ID;
        speedIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            z-index: ${UI_CONFIG.Z_INDEX};
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;
        document.body.appendChild(speedIndicator);
    }

    /**
     * 显示指示器
     */
    function showIndicator(text) {
        if (!speedIndicator) return;

        speedIndicator.textContent = text;
        speedIndicator.style.opacity = '1';

        clearTimeout(showIndicator.timer);
        showIndicator.timer = setTimeout(() => {
            speedIndicator.style.opacity = '0';
        }, UI_CONFIG.INDICATOR_DISPLAY_TIME);
    }

    /**
     * 创建视频速度提示器
     */
    function createSpeedOverlay(video) {
        if (!video || document.getElementById(UI_CONFIG.SPEED_OVERLAY_ID)) return;

        speedOverlay = document.createElement('div');
        speedOverlay.id = UI_CONFIG.SPEED_OVERLAY_ID;
        speedOverlay.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 16px;
            font-weight: bold;
            z-index: ${UI_CONFIG.Z_INDEX};
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;

        // 将提示器添加到视频的父容器中
        const videoContainer = video.parentElement;
        if (videoContainer) {
            // 确保父容器有相对定位
            const containerStyle = window.getComputedStyle(videoContainer);
            if (containerStyle.position === 'static') {
                videoContainer.style.position = 'relative';
            }
            videoContainer.appendChild(speedOverlay);
        }
    }

    /**
     * 显示视频速度提示
     */
    function showSpeedOverlay(video, speed) {
        if (!video) return;

        // 如果提示器不存在，创建它
        if (!speedOverlay) {
            createSpeedOverlay(video);
        }

        if (!speedOverlay) return;

        speedOverlay.textContent = `${speed}x`;
        speedOverlay.style.opacity = '1';

        clearTimeout(showSpeedOverlay.timer);
        showSpeedOverlay.timer = setTimeout(() => {
            speedOverlay.style.opacity = '0';
        }, UI_CONFIG.SPEED_DISPLAY_TIME);
    }

    /**
     * 获取当前活动的视频元素
     */
    function getCurrentVideo() {
        // 如果在lightbox模式，优先返回lightbox中的视频
        if (isLightboxActive && lightboxOverlay) {
            const lightboxVideo = lightboxOverlay.querySelector('video');
            if (lightboxVideo) return lightboxVideo;
        }

        // 寻找页面中的视频元素
        const videos = Array.from(document.querySelectorAll('video'));
        if (videos.length === 0) return null;

        // 优先选择正在播放的视频
        const playingVideo = videos.find(v => !v.paused);
        if (playingVideo) return playingVideo;

        // 选择最大的可见视频
        let bestVideo = null;
        let maxArea = 0;

        for (const video of videos) {
            const rect = video.getBoundingClientRect();
            const area = rect.width * rect.height;

            if (area > maxArea && isElementVisible(video)) {
                maxArea = area;
                bestVideo = video;
            }
        }

        return bestVideo || videos[0];
    }

    /**
     * 检查元素是否可见
     */
    function isElementVisible(element) {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0 &&
            rect.top < window.innerHeight && rect.bottom > 0;
    }

    /**
     * 调整播放速度
     */
    function adjustSpeed(video, delta) {
        if (!video) return false;

        const newSpeed = Math.max(
            MEDIA_CONFIG.MIN_SPEED,
            Math.min(MEDIA_CONFIG.MAX_SPEED, video.playbackRate + delta)
        );

        video.playbackRate = newSpeed;
        showSpeedOverlay(video, newSpeed.toFixed(2));
        return true;
    }

    /**
     * 重置播放速度
     */
    function resetSpeed(video) {
        if (!video) return false;

        video.playbackRate = MEDIA_CONFIG.DEFAULT_SPEED;
        showSpeedOverlay(video, MEDIA_CONFIG.DEFAULT_SPEED.toFixed(1));
        return true;
    }

    /**
     * 快进/快退
     */
    function seek(video, seconds) {
        if (!video) return false;

        const newTime = Math.max(0, Math.min(video.duration || 0, video.currentTime + seconds));
        video.currentTime = newTime;

        const direction = seconds > 0 ? '快进' : '快退';
        showIndicator(`${direction} ${Math.abs(seconds)}秒`);
        return true;
    }

    /**
     * 切换网页全屏
     */
    function toggleLightboxFullscreen(video) {
        if (!video || video.tagName !== 'VIDEO') return false;

        if (isLightboxActive) {
            exitLightbox();
        } else {
            enterLightbox(video);
        }
        return true;
    }

    /**
     * 进入网页全屏
     */
    function enterLightbox(video) {
        // 保存原始视频状态
        originalVideoState = {
            parent: video.parentElement,
            nextSibling: video.nextElementSibling,
            originalStyle: video.style.cssText,
            originalPosition: video.style.position,
            originalTop: video.style.top,
            originalLeft: video.style.left,
            originalWidth: video.style.width,
            originalHeight: video.style.height,
            originalMaxWidth: video.style.maxWidth,
            originalMaxHeight: video.style.maxHeight,
            originalZIndex: video.style.zIndex
        };

        // 创建全屏覆盖层
        lightboxOverlay = document.createElement('div');
        lightboxOverlay.id = UI_CONFIG.LIGHTBOX_OVERLAY_ID;
        lightboxOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: black;
            z-index: ${UI_CONFIG.Z_INDEX - 1};
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // 将原视频元素移动到全屏覆盖层中，而不是克隆
        video.style.cssText = `
            max-width: 100vw !important;
            max-height: 100vh !important;
            width: auto !important;
            height: auto !important;
            position: static !important;
            z-index: auto !important;
        `;

        lightboxOverlay.appendChild(video);
        document.body.appendChild(lightboxOverlay);
        document.body.style.overflow = 'hidden';

        isLightboxActive = true;
        showIndicator('进入网页全屏');

        // ESC键退出
        lightboxOverlay.addEventListener('click', exitLightbox);
    }

    /**
     * 退出网页全屏
     */
    function exitLightbox() {
        if (!lightboxOverlay || !originalVideoState) return;

        const lightboxVideo = lightboxOverlay.querySelector('video');

        if (lightboxVideo) {
            // 恢复视频元素的原始样式
            lightboxVideo.style.cssText = originalVideoState.originalStyle;

            // 将视频元素移回原位置
            if (originalVideoState.nextSibling) {
                originalVideoState.parent.insertBefore(lightboxVideo, originalVideoState.nextSibling);
            } else {
                originalVideoState.parent.appendChild(lightboxVideo);
            }
        }

        // 移除覆盖层
        document.body.removeChild(lightboxOverlay);
        document.body.style.overflow = '';
        lightboxOverlay = null;
        originalVideoState = null;
        isLightboxActive = false;

        showIndicator('退出网页全屏');
    }

    /**
     * 设置键盘监听器
     */
    function setupKeyboardListeners() {
        document.addEventListener('keydown', handleKeydown, true);
    }

    /**
     * 处理键盘事件
     */
    function handleKeydown(event) {
        // 忽略在输入框中的按键
        if (event.target.tagName === 'INPUT' ||
            event.target.tagName === 'TEXTAREA' ||
            event.target.isContentEditable) {
            return;
        }

        // ESC键特殊处理
        if (event.key === 'Escape' && isLightboxActive) {
            event.preventDefault();
            exitLightbox();
            return;
        }

        // 获取当前视频
        currentVideo = getCurrentVideo();
        if (!currentVideo) return;

        let handled = false;

        // 处理快捷键
        switch (event.key) {
            case SHORTCUTS.increase:
                handled = adjustSpeed(currentVideo, MEDIA_CONFIG.SPEED_STEP);
                break;

            case SHORTCUTS.decrease:
                handled = adjustSpeed(currentVideo, -MEDIA_CONFIG.SPEED_STEP);
                break;

            case SHORTCUTS.reset:
                handled = resetSpeed(currentVideo);
                break;

            case SHORTCUTS.fullscreen:
                handled = toggleLightboxFullscreen(currentVideo);
                break;

            case SHORTCUTS.seekForward:
                // 仅在lightbox模式下处理方向键
                if (isLightboxActive) {
                    handled = seek(currentVideo, MEDIA_CONFIG.SEEK_STEP);
                }
                break;

            case SHORTCUTS.seekBackward:
                // 仅在lightbox模式下处理方向键
                if (isLightboxActive) {
                    handled = seek(currentVideo, -MEDIA_CONFIG.SEEK_STEP);
                }
                break;
        }

        if (handled) {
            event.preventDefault();
            event.stopPropagation();
        }
    }

    /**
     * 清理资源
     */
    function cleanup() {
        if (speedIndicator && speedIndicator.parentNode) {
            speedIndicator.parentNode.removeChild(speedIndicator);
        }
        if (isLightboxActive) {
            exitLightbox();
        }
        document.removeEventListener('keydown', handleKeydown, true);
    }

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);

})();
