# 贡献指南

感谢您对视频 & 音频速度控制器项目的关注！我们欢迎各种形式的贡献，包括但不限于功能请求、bug报告、文档改进、代码贡献等。本指南将帮助您了解如何参与项目开发。

## 目录

- [行为准则](#行为准则)
- [贡献流程](#贡献流程)
- [开发环境](#开发环境)
- [代码风格](#代码风格)
- [提交Pull Request](#提交pull-request)
- [问题报告](#问题报告)
- [功能请求](#功能请求)

## 行为准则

请在参与本项目时遵循以下原则：

- 使用友好、包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注项目和社区的最佳利益
- 对其他社区成员表示同理心

## 贡献流程

1. **Fork项目仓库**
   - 点击GitHub页面右上角的"Fork"按钮，将项目复制到您的GitHub账户下

2. **克隆您的Fork**
   ```bash
   git clone https://github.com/您的用户名/chrome-plugin-video-manager.git
   cd chrome-plugin-video-manager
   ```

3. **创建新分支**
   ```bash
   git checkout -b feature/your-feature-name
   # 或者
   git checkout -b fix/your-bugfix-name
   ```

4. **进行更改**
   - 编写代码
   - 添加或更新测试
   - 更新相关文档

5. **本地测试**
   - 在Chrome浏览器中加载您的开发版本
   - 确保功能正常工作，且没有引入新的问题

6. **提交更改**
   ```bash
   git add .
   git commit -m "简洁明了的提交信息"
   ```

7. **推送到您的Fork**
   ```bash
   git push origin feature/your-feature-name
   ```

8. **创建Pull Request**
   - 访问GitHub上的原始仓库
   - 点击"Pull requests"标签，然后点击"New pull request"
   - 选择"compare across forks"，选择您的Fork和分支
   - 填写PR描述，说明您的更改

## 开发环境

本项目是一个纯JavaScript的Chrome扩展，不需要复杂的构建工具。

### 基本要求

- 最新版本的Chrome浏览器
- 基本的代码编辑器（推荐Visual Studio Code、Sublime Text等）

### 调试扩展

1. 在Chrome地址栏中输入 `chrome://extensions/`
2. 启用右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"，选择项目文件夹
4. 要在更改代码后更新扩展，点击扩展管理页面中的刷新图标

### 检查控制台日志

1. 右键点击扩展图标，选择"检查弹出内容"查看选项页面的日志
2. 在任何网页上右键点击，选择"检查"，然后转到"控制台"标签，查看content.js的日志

## 代码风格

我们遵循以下代码风格指南：

### JavaScript

- 使用2个或4个空格缩进（保持一致）
- 使用分号结束语句
- 使用单引号表示字符串
- 每行长度不超过100个字符
- 变量和函数使用camelCase命名
- 常量使用UPPER_CASE命名
- 在适当的地方添加注释
- 所有功能应有错误处理

### CSS

- 使用2个或4个空格缩进（保持一致）
- 类名使用小写和连字符（如 `my-class-name`）
- 属性声明后使用分号
- 每个选择器和声明块后留一个空行

### 提交消息

- 使用现在时态（"Add feature"而非"Added feature"）
- 第一行是简短的描述（不超过50个字符）
- 必要时，在空行后添加更详细的描述
- 描述变更的内容和原因，而不是如何变更

## 提交Pull Request

提交PR时，请确保：

- PR标题清晰描述了变更内容
- PR描述包含以下信息：
  - 变更的目的
  - 解决的问题
  - 实现的方法
  - 任何需要审阅者特别关注的部分
- 您的代码通过了自测
- 您已更新了相关文档
- 如果添加了新功能，考虑同时添加测试
- 如果修复了bug，考虑添加防止回归的测试

## 问题报告

报告问题时，请包含以下信息：

- 问题的清晰描述
- 重现步骤
- 预期行为与实际行为
- 相关的屏幕截图（如适用）
- 浏览器版本
- 操作系统
- 扩展版本

## 功能请求

提出功能请求时，请考虑：

- 该功能是否符合项目的整体目标
- 该功能将如何帮助用户
- 您是否愿意实现该功能

提交功能请求时，请：

- 使用清晰的标题
- 详细描述功能及其用例
- 说明为什么该功能对用户有价值
- 提供任何可能的替代方案

---

感谢您的贡献！您的努力将帮助我们使视频 & 音频速度控制器变得更好。 