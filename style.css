/**
 * 视频速度控制器样式 - 极简版
 * 仅保留必要的UI组件样式
 */

/* 速度指示器基础样式（内联样式的备份） */
#video-speed-indicator {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    font-family: monospace !important;
    font-size: 14px !important;
    z-index: 2147483647 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none !important;
}

/* 视频速度提示器样式 */
#video-speed-overlay {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 4px !important;
    font-family: monospace !important;
    font-size: 16px !important;
    font-weight: bold !important;
    z-index: 2147483647 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none !important;
}

/* 网页全屏覆盖层基础样式（内联样式的备份） */
#video-lightbox-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: black !important;
    z-index: 2147483646 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* 网页全屏视频样式 */
#video-lightbox-overlay video {
    max-width: 100vw !important;
    max-height: 100vh !important;
    width: auto !important;
    height: auto !important;
}

/* 防止页面滚动样式 */
body.vsc-lightbox-active {
    overflow: hidden !important;
}