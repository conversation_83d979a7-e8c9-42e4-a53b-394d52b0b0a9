# 开发指南

## 项目结构

```
chrome-plugin-video-manager/
├── src/                    # 源代码
│   ├── config.js          # 配置文件
│   ├── utils.js           # 工具函数
│   ├── errorHandler.js    # 错误处理
│   ├── mediaDetector.js   # 媒体检测
│   ├── shortcutManager.js # 快捷键管理
│   ├── playbackController.js # 播放控制
│   ├── uiManager.js       # UI管理
│   ├── videoSpeedController.js # 主控制器
│   ├── main.js            # 主入口
│   └── content.js         # 内容脚本入口
├── tests/                 # 测试文件
│   ├── setup.js           # 测试环境设置
│   ├── config.test.js     # 配置测试
│   ├── utils.test.js      # 工具函数测试
│   └── playbackController.test.js # 播放控制器测试
├── options.html           # 设置页面
├── options.js             # 设置页面脚本
├── style.css              # 样式文件
├── manifest.json          # 扩展清单
├── test.html              # 测试页面
└── 配置文件...
```

## 开发环境设置

### 1. 安装依赖

```bash
npm install
```

### 2. 开发工具

#### 代码检查 (ESLint)
```bash
# 检查代码
npm run lint

# 自动修复
npm run lint:fix
```

#### 代码格式化 (Prettier)
```bash
# 格式化代码
npm run format

# 检查格式
npm run format:check
```

#### 单元测试 (Jest)
```bash
# 运行所有测试
npm test

# 监视模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 3. 开发流程

#### 日常开发
```bash
# 启动开发模式（自动修复代码风格 + 监视测试）
npm run dev
```

#### 代码验证
```bash
# 完整验证（代码检查 + 测试 + 格式检查）
npm run validate
```

#### 构建发布
```bash
# 开发构建
npm run build

# 生产构建
npm run build:prod
```

## 代码规范

### JavaScript 规范

1. **使用ES6+语法**
   - 优先使用 `const` 和 `let`
   - 使用箭头函数
   - 使用模板字符串
   - 使用解构赋值

2. **命名规范**
   - 变量和函数使用驼峰命名法
   - 常量使用大写字母和下划线
   - 类名使用帕斯卡命名法

3. **函数规范**
   - 单个函数不超过50行
   - 参数不超过5个
   - 使用JSDoc注释

4. **错误处理**
   - 使用try-catch包装可能出错的代码
   - 使用错误处理模块统一处理错误
   - 提供用户友好的错误信息

### 测试规范

1. **测试覆盖率**
   - 分支覆盖率 ≥ 70%
   - 函数覆盖率 ≥ 70%
   - 行覆盖率 ≥ 70%
   - 语句覆盖率 ≥ 70%

2. **测试结构**
   - 使用describe分组测试
   - 使用有意义的测试描述
   - 每个测试只验证一个功能点

3. **测试数据**
   - 使用testUtils提供的工具函数
   - 模拟外部依赖
   - 清理测试环境

## 调试指南

### 1. 浏览器调试

在Chrome中加载扩展：
1. 打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目根目录

### 2. 测试页面

使用 `test.html` 进行功能测试：
1. 在浏览器中打开 `test.html`
2. 使用快捷键测试功能
3. 查看控制台输出

### 3. 性能监控

启用性能监控：
```javascript
// 在config.js中设置
DEBUG_CONFIG.ENABLED = true;
DEBUG_CONFIG.PERFORMANCE_MONITORING = true;
```

查看性能报告：
- 使用 `Ctrl+Shift+P` 显示性能报告
- 查看控制台的详细性能日志

### 4. 错误追踪

查看错误统计：
```javascript
// 在控制台中执行
window.videoSpeedController.getErrorStats();
```

## 贡献指南

### 1. 提交前检查

```bash
# 运行完整验证
npm run validate

# 或使用预提交钩子
npm run precommit
```

### 2. 提交信息格式

```
type(scope): description

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建工具或辅助工具的变动

### 3. 分支策略

- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `fix/*`: 修复分支

## 常见问题

### Q: 测试失败怎么办？
A: 
1. 检查测试环境设置
2. 确保模拟的API正确
3. 查看错误信息定位问题

### Q: ESLint报错怎么办？
A:
1. 运行 `npm run lint:fix` 自动修复
2. 手动修复无法自动修复的问题
3. 必要时调整ESLint规则

### Q: 如何添加新的测试？
A:
1. 在 `tests/` 目录下创建 `.test.js` 文件
2. 使用Jest和testUtils编写测试
3. 确保测试覆盖率达标

### Q: 如何调试Chrome扩展？
A:
1. 在扩展管理页面点击"检查视图"
2. 使用Chrome开发者工具
3. 查看控制台输出和网络请求

## 性能优化建议

1. **缓存策略**
   - 合理设置缓存过期时间
   - 避免频繁的DOM查询
   - 使用WeakMap存储元素相关数据

2. **事件处理**
   - 使用防抖和节流技术
   - 及时清理事件监听器
   - 避免在高频事件中执行重操作

3. **内存管理**
   - 及时清理不需要的引用
   - 使用WeakMap和WeakSet避免内存泄漏
   - 定期清理过期缓存

4. **DOM操作**
   - 批量操作DOM
   - 使用DocumentFragment
   - 避免强制同步布局
